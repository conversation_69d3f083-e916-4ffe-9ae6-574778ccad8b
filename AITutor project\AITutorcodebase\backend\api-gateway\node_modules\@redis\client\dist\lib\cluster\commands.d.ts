import * as APPEND from '../commands/APPEND';
import * as BITCOUNT from '../commands/BITCOUNT';
import * as BITFIELD_RO from '../commands/BITFIELD_RO';
import * as BITFIELD from '../commands/BITFIELD';
import * as <PERSON><PERSON><PERSON> from '../commands/BITOP';
import * as BITPOS from '../commands/BITPOS';
import * as <PERSON><PERSON>OVE from '../commands/BLMOVE';
import * as BLMPOP from '../commands/BLMPOP';
import * as BLPOP from '../commands/BLPOP';
import * as BRPOP from '../commands/BRPOP';
import * as BRPOPLPUSH from '../commands/BRPOPLPUSH';
import * as <PERSON><PERSON>MPOP from '../commands/BZMPOP';
import * as BZ<PERSON><PERSON><PERSON>X from '../commands/BZPOPMAX';
import * as BZPOPMIN from '../commands/BZPOPMIN';
import * as COPY from '../commands/COPY';
import * as <PERSON><PERSON> from '../commands/DECR';
import * as DECRBY from '../commands/DECRBY';
import * as <PERSON><PERSON> from '../commands/DEL';
import * as DUMP from '../commands/DUMP';
import * as EVAL_RO from '../commands/EVAL_RO';
import * as EVAL from '../commands/EVAL';
import * as EVALSHA_RO from '../commands/EVALSHA_RO';
import * as EVALSHA from '../commands/EVALSHA';
import * as EXISTS from '../commands/EXISTS';
import * as EXPIRE from '../commands/EXPIRE';
import * as EXPIREAT from '../commands/EXPIREAT';
import * as EXPIRETIME from '../commands/EXPIRETIME';
import * as FCALL_RO from '../commands/FCALL_RO';
import * as FCALL from '../commands/FCALL';
import * as GEOADD from '../commands/GEOADD';
import * as GEODIST from '../commands/GEODIST';
import * as GEOHASH from '../commands/GEOHASH';
import * as GEOPOS from '../commands/GEOPOS';
import * as GEORADIUS_RO_WITH from '../commands/GEORADIUS_RO_WITH';
import * as GEORADIUS_RO from '../commands/GEORADIUS_RO';
import * as GEORADIUS_WITH from '../commands/GEORADIUS_WITH';
import * as GEORADIUS from '../commands/GEORADIUS';
import * as GEORADIUSBYMEMBER_RO_WITH from '../commands/GEORADIUSBYMEMBER_RO_WITH';
import * as GEORADIUSBYMEMBER_RO from '../commands/GEORADIUSBYMEMBER_RO';
import * as GEORADIUSBYMEMBER_WITH from '../commands/GEORADIUSBYMEMBER_WITH';
import * as GEORADIUSBYMEMBER from '../commands/GEORADIUSBYMEMBER';
import * as GEORADIUSBYMEMBERSTORE from '../commands/GEORADIUSBYMEMBERSTORE';
import * as GEORADIUSSTORE from '../commands/GEORADIUSSTORE';
import * as GEOSEARCH_WITH from '../commands/GEOSEARCH_WITH';
import * as GEOSEARCH from '../commands/GEOSEARCH';
import * as GEOSEARCHSTORE from '../commands/GEOSEARCHSTORE';
import * as GET from '../commands/GET';
import * as GETBIT from '../commands/GETBIT';
import * as GETDEL from '../commands/GETDEL';
import * as GETEX from '../commands/GETEX';
import * as GETRANGE from '../commands/GETRANGE';
import * as GETSET from '../commands/GETSET';
import * as HDEL from '../commands/HDEL';
import * as HEXISTS from '../commands/HEXISTS';
import * as HEXPIRE from '../commands/HEXPIRE';
import * as HEXPIREAT from '../commands/HEXPIREAT';
import * as HEXPIRETIME from '../commands/HEXPIRETIME';
import * as HGET from '../commands/HGET';
import * as HGETALL from '../commands/HGETALL';
import * as HINCRBY from '../commands/HINCRBY';
import * as HINCRBYFLOAT from '../commands/HINCRBYFLOAT';
import * as HKEYS from '../commands/HKEYS';
import * as HLEN from '../commands/HLEN';
import * as HMGET from '../commands/HMGET';
import * as HPERSIST from '../commands/HPERSIST';
import * as HPEXPIRE from '../commands/HPEXPIRE';
import * as HPEXPIREAT from '../commands/HPEXPIREAT';
import * as HPEXPIRETIME from '../commands/HPEXPIRETIME';
import * as HPTTL from '../commands/HPTTL';
import * as HRANDFIELD_COUNT_WITHVALUES from '../commands/HRANDFIELD_COUNT_WITHVALUES';
import * as HRANDFIELD_COUNT from '../commands/HRANDFIELD_COUNT';
import * as HRANDFIELD from '../commands/HRANDFIELD';
import * as HSCAN from '../commands/HSCAN';
import * as HSCAN_NOVALUES from '../commands/HSCAN_NOVALUES';
import * as HSET from '../commands/HSET';
import * as HSETNX from '../commands/HSETNX';
import * as HSTRLEN from '../commands/HSTRLEN';
import * as HTTL from '../commands/HTTL';
import * as HVALS from '../commands/HVALS';
import * as INCR from '../commands/INCR';
import * as INCRBY from '../commands/INCRBY';
import * as INCRBYFLOAT from '../commands/INCRBYFLOAT';
import * as LCS_IDX_WITHMATCHLEN from '../commands/LCS_IDX_WITHMATCHLEN';
import * as LCS_IDX from '../commands/LCS_IDX';
import * as LCS_LEN from '../commands/LCS_LEN';
import * as LCS from '../commands/LCS';
import * as LINDEX from '../commands/LINDEX';
import * as LINSERT from '../commands/LINSERT';
import * as LLEN from '../commands/LLEN';
import * as LMOVE from '../commands/LMOVE';
import * as LMPOP from '../commands/LMPOP';
import * as LPOP_COUNT from '../commands/LPOP_COUNT';
import * as LPOP from '../commands/LPOP';
import * as LPOS_COUNT from '../commands/LPOS_COUNT';
import * as LPOS from '../commands/LPOS';
import * as LPUSH from '../commands/LPUSH';
import * as LPUSHX from '../commands/LPUSHX';
import * as LRANGE from '../commands/LRANGE';
import * as LREM from '../commands/LREM';
import * as LSET from '../commands/LSET';
import * as LTRIM from '../commands/LTRIM';
import * as MGET from '../commands/MGET';
import * as MIGRATE from '../commands/MIGRATE';
import * as MSET from '../commands/MSET';
import * as MSETNX from '../commands/MSETNX';
import * as OBJECT_ENCODING from '../commands/OBJECT_ENCODING';
import * as OBJECT_FREQ from '../commands/OBJECT_FREQ';
import * as OBJECT_IDLETIME from '../commands/OBJECT_IDLETIME';
import * as OBJECT_REFCOUNT from '../commands/OBJECT_REFCOUNT';
import * as PERSIST from '../commands/PERSIST';
import * as PEXPIRE from '../commands/PEXPIRE';
import * as PEXPIREAT from '../commands/PEXPIREAT';
import * as PEXPIRETIME from '../commands/PEXPIRETIME';
import * as PFADD from '../commands/PFADD';
import * as PFCOUNT from '../commands/PFCOUNT';
import * as PFMERGE from '../commands/PFMERGE';
import * as PSETEX from '../commands/PSETEX';
import * as PTTL from '../commands/PTTL';
import * as PUBLISH from '../commands/PUBLISH';
import * as RENAME from '../commands/RENAME';
import * as RENAMENX from '../commands/RENAMENX';
import * as RESTORE from '../commands/RESTORE';
import * as RPOP_COUNT from '../commands/RPOP_COUNT';
import * as RPOP from '../commands/RPOP';
import * as RPOPLPUSH from '../commands/RPOPLPUSH';
import * as RPUSH from '../commands/RPUSH';
import * as RPUSHX from '../commands/RPUSHX';
import * as SADD from '../commands/SADD';
import * as SCARD from '../commands/SCARD';
import * as SDIFF from '../commands/SDIFF';
import * as SDIFFSTORE from '../commands/SDIFFSTORE';
import * as SET from '../commands/SET';
import * as SETBIT from '../commands/SETBIT';
import * as SETEX from '../commands/SETEX';
import * as SETNX from '../commands/SETNX';
import * as SETRANGE from '../commands/SETRANGE';
import * as SINTER from '../commands/SINTER';
import * as SINTERCARD from '../commands/SINTERCARD';
import * as SINTERSTORE from '../commands/SINTERSTORE';
import * as SISMEMBER from '../commands/SISMEMBER';
import * as SMEMBERS from '../commands/SMEMBERS';
import * as SMISMEMBER from '../commands/SMISMEMBER';
import * as SMOVE from '../commands/SMOVE';
import * as SORT_RO from '../commands/SORT_RO';
import * as SORT_STORE from '../commands/SORT_STORE';
import * as SORT from '../commands/SORT';
import * as SPOP from '../commands/SPOP';
import * as SPUBLISH from '../commands/SPUBLISH';
import * as SRANDMEMBER_COUNT from '../commands/SRANDMEMBER_COUNT';
import * as SRANDMEMBER from '../commands/SRANDMEMBER';
import * as SREM from '../commands/SREM';
import * as SSCAN from '../commands/SSCAN';
import * as STRLEN from '../commands/STRLEN';
import * as SUNION from '../commands/SUNION';
import * as SUNIONSTORE from '../commands/SUNIONSTORE';
import * as TOUCH from '../commands/TOUCH';
import * as TTL from '../commands/TTL';
import * as TYPE from '../commands/TYPE';
import * as UNLINK from '../commands/UNLINK';
import * as WATCH from '../commands/WATCH';
import * as XACK from '../commands/XACK';
import * as XADD from '../commands/XADD';
import * as XAUTOCLAIM_JUSTID from '../commands/XAUTOCLAIM_JUSTID';
import * as XAUTOCLAIM from '../commands/XAUTOCLAIM';
import * as XCLAIM_JUSTID from '../commands/XCLAIM_JUSTID';
import * as XCLAIM from '../commands/XCLAIM';
import * as XDEL from '../commands/XDEL';
import * as XGROUP_CREATE from '../commands/XGROUP_CREATE';
import * as XGROUP_CREATECONSUMER from '../commands/XGROUP_CREATECONSUMER';
import * as XGROUP_DELCONSUMER from '../commands/XGROUP_DELCONSUMER';
import * as XGROUP_DESTROY from '../commands/XGROUP_DESTROY';
import * as XGROUP_SETID from '../commands/XGROUP_SETID';
import * as XINFO_CONSUMERS from '../commands/XINFO_CONSUMERS';
import * as XINFO_GROUPS from '../commands/XINFO_GROUPS';
import * as XINFO_STREAM from '../commands/XINFO_STREAM';
import * as XLEN from '../commands/XLEN';
import * as XPENDING_RANGE from '../commands/XPENDING_RANGE';
import * as XPENDING from '../commands/XPENDING';
import * as XRANGE from '../commands/XRANGE';
import * as XREAD from '../commands/XREAD';
import * as XREADGROUP from '../commands/XREADGROUP';
import * as XREVRANGE from '../commands/XREVRANGE';
import * as XSETID from '../commands/XSETID';
import * as XTRIM from '../commands/XTRIM';
import * as ZADD from '../commands/ZADD';
import * as ZCARD from '../commands/ZCARD';
import * as ZCOUNT from '../commands/ZCOUNT';
import * as ZDIFF_WITHSCORES from '../commands/ZDIFF_WITHSCORES';
import * as ZDIFF from '../commands/ZDIFF';
import * as ZDIFFSTORE from '../commands/ZDIFFSTORE';
import * as ZINCRBY from '../commands/ZINCRBY';
import * as ZINTER_WITHSCORES from '../commands/ZINTER_WITHSCORES';
import * as ZINTER from '../commands/ZINTER';
import * as ZINTERCARD from '../commands/ZINTERCARD';
import * as ZINTERSTORE from '../commands/ZINTERSTORE';
import * as ZLEXCOUNT from '../commands/ZLEXCOUNT';
import * as ZMPOP from '../commands/ZMPOP';
import * as ZMSCORE from '../commands/ZMSCORE';
import * as ZPOPMAX_COUNT from '../commands/ZPOPMAX_COUNT';
import * as ZPOPMAX from '../commands/ZPOPMAX';
import * as ZPOPMIN_COUNT from '../commands/ZPOPMIN_COUNT';
import * as ZPOPMIN from '../commands/ZPOPMIN';
import * as ZRANDMEMBER_COUNT_WITHSCORES from '../commands/ZRANDMEMBER_COUNT_WITHSCORES';
import * as ZRANDMEMBER_COUNT from '../commands/ZRANDMEMBER_COUNT';
import * as ZRANDMEMBER from '../commands/ZRANDMEMBER';
import * as ZRANGE_WITHSCORES from '../commands/ZRANGE_WITHSCORES';
import * as ZRANGE from '../commands/ZRANGE';
import * as ZRANGEBYLEX from '../commands/ZRANGEBYLEX';
import * as ZRANGEBYSCORE_WITHSCORES from '../commands/ZRANGEBYSCORE_WITHSCORES';
import * as ZRANGEBYSCORE from '../commands/ZRANGEBYSCORE';
import * as ZRANGESTORE from '../commands/ZRANGESTORE';
import * as ZRANK from '../commands/ZRANK';
import * as ZREM from '../commands/ZREM';
import * as ZREMRANGEBYLEX from '../commands/ZREMRANGEBYLEX';
import * as ZREMRANGEBYRANK from '../commands/ZREMRANGEBYRANK';
import * as ZREMRANGEBYSCORE from '../commands/ZREMRANGEBYSCORE';
import * as ZREVRANK from '../commands/ZREVRANK';
import * as ZSCAN from '../commands/ZSCAN';
import * as ZSCORE from '../commands/ZSCORE';
import * as ZUNION_WITHSCORES from '../commands/ZUNION_WITHSCORES';
import * as ZUNION from '../commands/ZUNION';
import * as ZUNIONSTORE from '../commands/ZUNIONSTORE';
declare const _default: {
    APPEND: typeof APPEND;
    append: typeof APPEND;
    BITCOUNT: typeof BITCOUNT;
    bitCount: typeof BITCOUNT;
    BITFIELD_RO: typeof BITFIELD_RO;
    bitFieldRo: typeof BITFIELD_RO;
    BITFIELD: typeof BITFIELD;
    bitField: typeof BITFIELD;
    BITOP: typeof BITOP;
    bitOp: typeof BITOP;
    BITPOS: typeof BITPOS;
    bitPos: typeof BITPOS;
    BLMOVE: typeof BLMOVE;
    blMove: typeof BLMOVE;
    BLMPOP: typeof BLMPOP;
    blmPop: typeof BLMPOP;
    BLPOP: typeof BLPOP;
    blPop: typeof BLPOP;
    BRPOP: typeof BRPOP;
    brPop: typeof BRPOP;
    BRPOPLPUSH: typeof BRPOPLPUSH;
    brPopLPush: typeof BRPOPLPUSH;
    BZMPOP: typeof BZMPOP;
    bzmPop: typeof BZMPOP;
    BZPOPMAX: typeof BZPOPMAX;
    bzPopMax: typeof BZPOPMAX;
    BZPOPMIN: typeof BZPOPMIN;
    bzPopMin: typeof BZPOPMIN;
    COPY: typeof COPY;
    copy: typeof COPY;
    DECR: typeof DECR;
    decr: typeof DECR;
    DECRBY: typeof DECRBY;
    decrBy: typeof DECRBY;
    DEL: typeof DEL;
    del: typeof DEL;
    DUMP: typeof DUMP;
    dump: typeof DUMP;
    EVAL_RO: typeof EVAL_RO;
    evalRo: typeof EVAL_RO;
    EVAL: typeof EVAL;
    eval: typeof EVAL;
    EVALSHA: typeof EVALSHA;
    evalSha: typeof EVALSHA;
    EVALSHA_RO: typeof EVALSHA_RO;
    evalShaRo: typeof EVALSHA_RO;
    EXISTS: typeof EXISTS;
    exists: typeof EXISTS;
    EXPIRE: typeof EXPIRE;
    expire: typeof EXPIRE;
    EXPIREAT: typeof EXPIREAT;
    expireAt: typeof EXPIREAT;
    EXPIRETIME: typeof EXPIRETIME;
    expireTime: typeof EXPIRETIME;
    FCALL_RO: typeof FCALL_RO;
    fCallRo: typeof FCALL_RO;
    FCALL: typeof FCALL;
    fCall: typeof FCALL;
    GEOADD: typeof GEOADD;
    geoAdd: typeof GEOADD;
    GEODIST: typeof GEODIST;
    geoDist: typeof GEODIST;
    GEOHASH: typeof GEOHASH;
    geoHash: typeof GEOHASH;
    GEOPOS: typeof GEOPOS;
    geoPos: typeof GEOPOS;
    GEORADIUS_RO_WITH: typeof GEORADIUS_RO_WITH;
    geoRadiusRoWith: typeof GEORADIUS_RO_WITH;
    GEORADIUS_RO: typeof GEORADIUS_RO;
    geoRadiusRo: typeof GEORADIUS_RO;
    GEORADIUS_WITH: typeof GEORADIUS_WITH;
    geoRadiusWith: typeof GEORADIUS_WITH;
    GEORADIUS: typeof GEORADIUS;
    geoRadius: typeof GEORADIUS;
    GEORADIUSBYMEMBER_RO_WITH: typeof GEORADIUSBYMEMBER_RO_WITH;
    geoRadiusByMemberRoWith: typeof GEORADIUSBYMEMBER_RO_WITH;
    GEORADIUSBYMEMBER_RO: typeof GEORADIUSBYMEMBER_RO;
    geoRadiusByMemberRo: typeof GEORADIUSBYMEMBER_RO;
    GEORADIUSBYMEMBER_WITH: typeof GEORADIUSBYMEMBER_WITH;
    geoRadiusByMemberWith: typeof GEORADIUSBYMEMBER_WITH;
    GEORADIUSBYMEMBER: typeof GEORADIUSBYMEMBER;
    geoRadiusByMember: typeof GEORADIUSBYMEMBER;
    GEORADIUSBYMEMBERSTORE: typeof GEORADIUSBYMEMBERSTORE;
    geoRadiusByMemberStore: typeof GEORADIUSBYMEMBERSTORE;
    GEORADIUSSTORE: typeof GEORADIUSSTORE;
    geoRadiusStore: typeof GEORADIUSSTORE;
    GEOSEARCH_WITH: typeof GEOSEARCH_WITH;
    geoSearchWith: typeof GEOSEARCH_WITH;
    GEOSEARCH: typeof GEOSEARCH;
    geoSearch: typeof GEOSEARCH;
    GEOSEARCHSTORE: typeof GEOSEARCHSTORE;
    geoSearchStore: typeof GEOSEARCHSTORE;
    GET: typeof GET;
    get: typeof GET;
    GETBIT: typeof GETBIT;
    getBit: typeof GETBIT;
    GETDEL: typeof GETDEL;
    getDel: typeof GETDEL;
    GETEX: typeof GETEX;
    getEx: typeof GETEX;
    GETRANGE: typeof GETRANGE;
    getRange: typeof GETRANGE;
    GETSET: typeof GETSET;
    getSet: typeof GETSET;
    HDEL: typeof HDEL;
    hDel: typeof HDEL;
    HEXISTS: typeof HEXISTS;
    hExists: typeof HEXISTS;
    HEXPIRE: typeof HEXPIRE;
    hExpire: typeof HEXPIRE;
    HEXPIREAT: typeof HEXPIREAT;
    hExpireAt: typeof HEXPIREAT;
    HEXPIRETIME: typeof HEXPIRETIME;
    hExpireTime: typeof HEXPIRETIME;
    HGET: typeof HGET;
    hGet: typeof HGET;
    HGETALL: typeof HGETALL;
    hGetAll: typeof HGETALL;
    HINCRBY: typeof HINCRBY;
    hIncrBy: typeof HINCRBY;
    HINCRBYFLOAT: typeof HINCRBYFLOAT;
    hIncrByFloat: typeof HINCRBYFLOAT;
    HKEYS: typeof HKEYS;
    hKeys: typeof HKEYS;
    HLEN: typeof HLEN;
    hLen: typeof HLEN;
    HMGET: typeof HMGET;
    hmGet: typeof HMGET;
    HPERSIST: typeof HPERSIST;
    hPersist: typeof HPERSIST;
    HPEXPIRE: typeof HPEXPIRE;
    hpExpire: typeof HPEXPIRE;
    HPEXPIREAT: typeof HPEXPIREAT;
    hpExpireAt: typeof HPEXPIREAT;
    HPEXPIRETIME: typeof HPEXPIRETIME;
    hpExpireTime: typeof HPEXPIRETIME;
    HPTTL: typeof HPTTL;
    hpTTL: typeof HPTTL;
    HRANDFIELD_COUNT_WITHVALUES: typeof HRANDFIELD_COUNT_WITHVALUES;
    hRandFieldCountWithValues: typeof HRANDFIELD_COUNT_WITHVALUES;
    HRANDFIELD_COUNT: typeof HRANDFIELD_COUNT;
    hRandFieldCount: typeof HRANDFIELD_COUNT;
    HRANDFIELD: typeof HRANDFIELD;
    hRandField: typeof HRANDFIELD;
    HSCAN: typeof HSCAN;
    hScan: typeof HSCAN;
    HSCAN_NOVALUES: typeof HSCAN_NOVALUES;
    hScanNoValues: typeof HSCAN_NOVALUES;
    HSET: typeof HSET;
    hSet: typeof HSET;
    HSETNX: typeof HSETNX;
    hSetNX: typeof HSETNX;
    HSTRLEN: typeof HSTRLEN;
    hStrLen: typeof HSTRLEN;
    HTTL: typeof HTTL;
    hTTL: typeof HTTL;
    HVALS: typeof HVALS;
    hVals: typeof HVALS;
    INCR: typeof INCR;
    incr: typeof INCR;
    INCRBY: typeof INCRBY;
    incrBy: typeof INCRBY;
    INCRBYFLOAT: typeof INCRBYFLOAT;
    incrByFloat: typeof INCRBYFLOAT;
    LCS_IDX_WITHMATCHLEN: typeof LCS_IDX_WITHMATCHLEN;
    lcsIdxWithMatchLen: typeof LCS_IDX_WITHMATCHLEN;
    LCS_IDX: typeof LCS_IDX;
    lcsIdx: typeof LCS_IDX;
    LCS_LEN: typeof LCS_LEN;
    lcsLen: typeof LCS_LEN;
    LCS: typeof LCS;
    lcs: typeof LCS;
    LINDEX: typeof LINDEX;
    lIndex: typeof LINDEX;
    LINSERT: typeof LINSERT;
    lInsert: typeof LINSERT;
    LLEN: typeof LLEN;
    lLen: typeof LLEN;
    LMOVE: typeof LMOVE;
    lMove: typeof LMOVE;
    LMPOP: typeof LMPOP;
    lmPop: typeof LMPOP;
    LPOP_COUNT: typeof LPOP_COUNT;
    lPopCount: typeof LPOP_COUNT;
    LPOP: typeof LPOP;
    lPop: typeof LPOP;
    LPOS_COUNT: typeof LPOS_COUNT;
    lPosCount: typeof LPOS_COUNT;
    LPOS: typeof LPOS;
    lPos: typeof LPOS;
    LPUSH: typeof LPUSH;
    lPush: typeof LPUSH;
    LPUSHX: typeof LPUSHX;
    lPushX: typeof LPUSHX;
    LRANGE: typeof LRANGE;
    lRange: typeof LRANGE;
    LREM: typeof LREM;
    lRem: typeof LREM;
    LSET: typeof LSET;
    lSet: typeof LSET;
    LTRIM: typeof LTRIM;
    lTrim: typeof LTRIM;
    MGET: typeof MGET;
    mGet: typeof MGET;
    MIGRATE: typeof MIGRATE;
    migrate: typeof MIGRATE;
    MSET: typeof MSET;
    mSet: typeof MSET;
    MSETNX: typeof MSETNX;
    mSetNX: typeof MSETNX;
    OBJECT_ENCODING: typeof OBJECT_ENCODING;
    objectEncoding: typeof OBJECT_ENCODING;
    OBJECT_FREQ: typeof OBJECT_FREQ;
    objectFreq: typeof OBJECT_FREQ;
    OBJECT_IDLETIME: typeof OBJECT_IDLETIME;
    objectIdleTime: typeof OBJECT_IDLETIME;
    OBJECT_REFCOUNT: typeof OBJECT_REFCOUNT;
    objectRefCount: typeof OBJECT_REFCOUNT;
    PERSIST: typeof PERSIST;
    persist: typeof PERSIST;
    PEXPIRE: typeof PEXPIRE;
    pExpire: typeof PEXPIRE;
    PEXPIREAT: typeof PEXPIREAT;
    pExpireAt: typeof PEXPIREAT;
    PEXPIRETIME: typeof PEXPIRETIME;
    pExpireTime: typeof PEXPIRETIME;
    PFADD: typeof PFADD;
    pfAdd: typeof PFADD;
    PFCOUNT: typeof PFCOUNT;
    pfCount: typeof PFCOUNT;
    PFMERGE: typeof PFMERGE;
    pfMerge: typeof PFMERGE;
    PSETEX: typeof PSETEX;
    pSetEx: typeof PSETEX;
    PTTL: typeof PTTL;
    pTTL: typeof PTTL;
    PUBLISH: typeof PUBLISH;
    publish: typeof PUBLISH;
    RENAME: typeof RENAME;
    rename: typeof RENAME;
    RENAMENX: typeof RENAMENX;
    renameNX: typeof RENAMENX;
    RESTORE: typeof RESTORE;
    restore: typeof RESTORE;
    RPOP_COUNT: typeof RPOP_COUNT;
    rPopCount: typeof RPOP_COUNT;
    RPOP: typeof RPOP;
    rPop: typeof RPOP;
    RPOPLPUSH: typeof RPOPLPUSH;
    rPopLPush: typeof RPOPLPUSH;
    RPUSH: typeof RPUSH;
    rPush: typeof RPUSH;
    RPUSHX: typeof RPUSHX;
    rPushX: typeof RPUSHX;
    SADD: typeof SADD;
    sAdd: typeof SADD;
    SCARD: typeof SCARD;
    sCard: typeof SCARD;
    SDIFF: typeof SDIFF;
    sDiff: typeof SDIFF;
    SDIFFSTORE: typeof SDIFFSTORE;
    sDiffStore: typeof SDIFFSTORE;
    SINTER: typeof SINTER;
    sInter: typeof SINTER;
    SINTERCARD: typeof SINTERCARD;
    sInterCard: typeof SINTERCARD;
    SINTERSTORE: typeof SINTERSTORE;
    sInterStore: typeof SINTERSTORE;
    SET: typeof SET;
    set: typeof SET;
    SETBIT: typeof SETBIT;
    setBit: typeof SETBIT;
    SETEX: typeof SETEX;
    setEx: typeof SETEX;
    SETNX: typeof SETNX;
    setNX: typeof SETNX;
    SETRANGE: typeof SETRANGE;
    setRange: typeof SETRANGE;
    SISMEMBER: typeof SISMEMBER;
    sIsMember: typeof SISMEMBER;
    SMEMBERS: typeof SMEMBERS;
    sMembers: typeof SMEMBERS;
    SMISMEMBER: typeof SMISMEMBER;
    smIsMember: typeof SMISMEMBER;
    SMOVE: typeof SMOVE;
    sMove: typeof SMOVE;
    SORT_RO: typeof SORT_RO;
    sortRo: typeof SORT_RO;
    SORT_STORE: typeof SORT_STORE;
    sortStore: typeof SORT_STORE;
    SORT: typeof SORT;
    sort: typeof SORT;
    SPOP: typeof SPOP;
    sPop: typeof SPOP;
    SPUBLISH: typeof SPUBLISH;
    sPublish: typeof SPUBLISH;
    SRANDMEMBER_COUNT: typeof SRANDMEMBER_COUNT;
    sRandMemberCount: typeof SRANDMEMBER_COUNT;
    SRANDMEMBER: typeof SRANDMEMBER;
    sRandMember: typeof SRANDMEMBER;
    SREM: typeof SREM;
    sRem: typeof SREM;
    SSCAN: typeof SSCAN;
    sScan: typeof SSCAN;
    STRLEN: typeof STRLEN;
    strLen: typeof STRLEN;
    SUNION: typeof SUNION;
    sUnion: typeof SUNION;
    SUNIONSTORE: typeof SUNIONSTORE;
    sUnionStore: typeof SUNIONSTORE;
    TOUCH: typeof TOUCH;
    touch: typeof TOUCH;
    TTL: typeof TTL;
    ttl: typeof TTL;
    TYPE: typeof TYPE;
    type: typeof TYPE;
    UNLINK: typeof UNLINK;
    unlink: typeof UNLINK;
    WATCH: typeof WATCH;
    watch: typeof WATCH;
    XACK: typeof XACK;
    xAck: typeof XACK;
    XADD: typeof XADD;
    xAdd: typeof XADD;
    XAUTOCLAIM_JUSTID: typeof XAUTOCLAIM_JUSTID;
    xAutoClaimJustId: typeof XAUTOCLAIM_JUSTID;
    XAUTOCLAIM: typeof XAUTOCLAIM;
    xAutoClaim: typeof XAUTOCLAIM;
    XCLAIM: typeof XCLAIM;
    xClaim: typeof XCLAIM;
    XCLAIM_JUSTID: typeof XCLAIM_JUSTID;
    xClaimJustId: typeof XCLAIM_JUSTID;
    XDEL: typeof XDEL;
    xDel: typeof XDEL;
    XGROUP_CREATE: typeof XGROUP_CREATE;
    xGroupCreate: typeof XGROUP_CREATE;
    XGROUP_CREATECONSUMER: typeof XGROUP_CREATECONSUMER;
    xGroupCreateConsumer: typeof XGROUP_CREATECONSUMER;
    XGROUP_DELCONSUMER: typeof XGROUP_DELCONSUMER;
    xGroupDelConsumer: typeof XGROUP_DELCONSUMER;
    XGROUP_DESTROY: typeof XGROUP_DESTROY;
    xGroupDestroy: typeof XGROUP_DESTROY;
    XGROUP_SETID: typeof XGROUP_SETID;
    xGroupSetId: typeof XGROUP_SETID;
    XINFO_CONSUMERS: typeof XINFO_CONSUMERS;
    xInfoConsumers: typeof XINFO_CONSUMERS;
    XINFO_GROUPS: typeof XINFO_GROUPS;
    xInfoGroups: typeof XINFO_GROUPS;
    XINFO_STREAM: typeof XINFO_STREAM;
    xInfoStream: typeof XINFO_STREAM;
    XLEN: typeof XLEN;
    xLen: typeof XLEN;
    XPENDING_RANGE: typeof XPENDING_RANGE;
    xPendingRange: typeof XPENDING_RANGE;
    XPENDING: typeof XPENDING;
    xPending: typeof XPENDING;
    XRANGE: typeof XRANGE;
    xRange: typeof XRANGE;
    XREAD: typeof XREAD;
    xRead: typeof XREAD;
    XREADGROUP: typeof XREADGROUP;
    xReadGroup: typeof XREADGROUP;
    XREVRANGE: typeof XREVRANGE;
    xRevRange: typeof XREVRANGE;
    XSETID: typeof XSETID;
    xSetId: typeof XSETID;
    XTRIM: typeof XTRIM;
    xTrim: typeof XTRIM;
    ZADD: typeof ZADD;
    zAdd: typeof ZADD;
    ZCARD: typeof ZCARD;
    zCard: typeof ZCARD;
    ZCOUNT: typeof ZCOUNT;
    zCount: typeof ZCOUNT;
    ZDIFF_WITHSCORES: typeof ZDIFF_WITHSCORES;
    zDiffWithScores: typeof ZDIFF_WITHSCORES;
    ZDIFF: typeof ZDIFF;
    zDiff: typeof ZDIFF;
    ZDIFFSTORE: typeof ZDIFFSTORE;
    zDiffStore: typeof ZDIFFSTORE;
    ZINCRBY: typeof ZINCRBY;
    zIncrBy: typeof ZINCRBY;
    ZINTER_WITHSCORES: typeof ZINTER_WITHSCORES;
    zInterWithScores: typeof ZINTER_WITHSCORES;
    ZINTER: typeof ZINTER;
    zInter: typeof ZINTER;
    ZINTERCARD: typeof ZINTERCARD;
    zInterCard: typeof ZINTERCARD;
    ZINTERSTORE: typeof ZINTERSTORE;
    zInterStore: typeof ZINTERSTORE;
    ZLEXCOUNT: typeof ZLEXCOUNT;
    zLexCount: typeof ZLEXCOUNT;
    ZMPOP: typeof ZMPOP;
    zmPop: typeof ZMPOP;
    ZMSCORE: typeof ZMSCORE;
    zmScore: typeof ZMSCORE;
    ZPOPMAX_COUNT: typeof ZPOPMAX_COUNT;
    zPopMaxCount: typeof ZPOPMAX_COUNT;
    ZPOPMAX: typeof ZPOPMAX;
    zPopMax: typeof ZPOPMAX;
    ZPOPMIN_COUNT: typeof ZPOPMIN_COUNT;
    zPopMinCount: typeof ZPOPMIN_COUNT;
    ZPOPMIN: typeof ZPOPMIN;
    zPopMin: typeof ZPOPMIN;
    ZRANDMEMBER_COUNT_WITHSCORES: typeof ZRANDMEMBER_COUNT_WITHSCORES;
    zRandMemberCountWithScores: typeof ZRANDMEMBER_COUNT_WITHSCORES;
    ZRANDMEMBER_COUNT: typeof ZRANDMEMBER_COUNT;
    zRandMemberCount: typeof ZRANDMEMBER_COUNT;
    ZRANDMEMBER: typeof ZRANDMEMBER;
    zRandMember: typeof ZRANDMEMBER;
    ZRANGE_WITHSCORES: typeof ZRANGE_WITHSCORES;
    zRangeWithScores: typeof ZRANGE_WITHSCORES;
    ZRANGE: typeof ZRANGE;
    zRange: typeof ZRANGE;
    ZRANGEBYLEX: typeof ZRANGEBYLEX;
    zRangeByLex: typeof ZRANGEBYLEX;
    ZRANGEBYSCORE_WITHSCORES: typeof ZRANGEBYSCORE_WITHSCORES;
    zRangeByScoreWithScores: typeof ZRANGEBYSCORE_WITHSCORES;
    ZRANGEBYSCORE: typeof ZRANGEBYSCORE;
    zRangeByScore: typeof ZRANGEBYSCORE;
    ZRANGESTORE: typeof ZRANGESTORE;
    zRangeStore: typeof ZRANGESTORE;
    ZRANK: typeof ZRANK;
    zRank: typeof ZRANK;
    ZREM: typeof ZREM;
    zRem: typeof ZREM;
    ZREMRANGEBYLEX: typeof ZREMRANGEBYLEX;
    zRemRangeByLex: typeof ZREMRANGEBYLEX;
    ZREMRANGEBYRANK: typeof ZREMRANGEBYRANK;
    zRemRangeByRank: typeof ZREMRANGEBYRANK;
    ZREMRANGEBYSCORE: typeof ZREMRANGEBYSCORE;
    zRemRangeByScore: typeof ZREMRANGEBYSCORE;
    ZREVRANK: typeof ZREVRANK;
    zRevRank: typeof ZREVRANK;
    ZSCAN: typeof ZSCAN;
    zScan: typeof ZSCAN;
    ZSCORE: typeof ZSCORE;
    zScore: typeof ZSCORE;
    ZUNION_WITHSCORES: typeof ZUNION_WITHSCORES;
    zUnionWithScores: typeof ZUNION_WITHSCORES;
    ZUNION: typeof ZUNION;
    zUnion: typeof ZUNION;
    ZUNIONSTORE: typeof ZUNIONSTORE;
    zUnionStore: typeof ZUNIONSTORE;
};
export default _default;
